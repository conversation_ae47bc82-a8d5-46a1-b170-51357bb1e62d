#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include "RobotDriver.h"
#include <thread>
#include <chrono>
#include <atomic>

using namespace AnalysisRobot::Robot;
using namespace testing;

class RobotDriverTest : public ::testing::Test {
protected:
    void SetUp() override {
        driver = std::make_unique<RobotDriver>();
    }

    void TearDown() override {
        if (driver) {
            driver->disconnect();
        }
    }

    std::unique_ptr<RobotDriver> driver;
};

// ========== 基础功能测试 ==========

// 测试驱动初始化
TEST_F(RobotDriverTest, InitializeWithValidConfig) {
    RobotConfig config;
    config.ipAddress = "*************";
    config.port = 502;
    config.slaveId = 1;
    config.responseTimeout = 2000;

    // 注意：这个测试可能会失败，因为实际的机器人可能不存在
    // 在实际环境中，需要模拟MODBUS设备或使用虚拟机器人
    bool result = driver->initialize(config);
    
    // 检查初始化结果
    EXPECT_TRUE(result || !driver->getLastError().empty());
    EXPECT_EQ(driver->getStatus(), RobotStatus::DISCONNECTED);
}

// 测试无效配置的初始化
TEST_F(RobotDriverTest, InitializeWithInvalidConfig) {
    RobotConfig config;
    config.ipAddress = "";  // 无效IP地址
    config.port = 502;
    config.slaveId = 1;

    bool result = driver->initialize(config);
    EXPECT_FALSE(result);
    EXPECT_FALSE(driver->getLastError().empty());
}

// 测试状态回调
TEST_F(RobotDriverTest, StatusCallback) {
    bool callbackCalled = false;
    RobotStatus receivedStatus;
    std::string receivedMessage;

    driver->setStatusCallback([&](RobotStatus status, const std::string& message) {
        callbackCalled = true;
        receivedStatus = status;
        receivedMessage = message;
    });

    RobotConfig config;
    config.ipAddress = "*************";
    config.port = 502;
    config.slaveId = 1;

    driver->initialize(config);

    EXPECT_TRUE(callbackCalled);
    EXPECT_EQ(receivedStatus, RobotStatus::DISCONNECTED);
    EXPECT_FALSE(receivedMessage.empty());
}

// 测试连接状态检查
TEST_F(RobotDriverTest, ConnectionStatus) {
    EXPECT_FALSE(driver->isConnected());
    EXPECT_EQ(driver->getStatus(), RobotStatus::DISCONNECTED);
}

// ========== 状态查询测试 ==========

// 测试未连接时的位置获取
TEST_F(RobotDriverTest, GetCurrentPositionWithoutConnection) {
    Position pos = driver->getCurrentPosition();
    // 未连接时应该返回默认位置
    EXPECT_EQ(pos.x, 0.0);
    EXPECT_EQ(pos.y, 0.0);
    EXPECT_EQ(pos.z, 0.0);
    EXPECT_FALSE(driver->getLastError().empty());
    EXPECT_THAT(driver->getLastError(), HasSubstr("Not connected"));
}

// 测试未连接时的关节角度获取
TEST_F(RobotDriverTest, GetCurrentJointAnglesWithoutConnection) {
    JointAngles angles = driver->getCurrentJointAngles();
    // 未连接时应该返回默认角度
    EXPECT_EQ(angles.joint1, 0.0);
    EXPECT_EQ(angles.joint2, 0.0);
    EXPECT_EQ(angles.joint3, 0.0);
    EXPECT_FALSE(driver->getLastError().empty());
    EXPECT_THAT(driver->getLastError(), HasSubstr("Not connected"));
}

// 测试未连接时的错误代码获取
TEST_F(RobotDriverTest, GetErrorCodeWithoutConnection) {
    int errorCode = driver->getErrorCode();
    EXPECT_EQ(errorCode, -1);  // 未连接时返回-1
}

// ========== 运动控制测试 ==========

// 测试未连接时的回原点
TEST_F(RobotDriverTest, HomeWithoutConnection) {
    bool result = driver->home();
    EXPECT_FALSE(result);
    EXPECT_FALSE(driver->getLastError().empty());
    EXPECT_THAT(driver->getLastError(), HasSubstr("Not connected"));
}

// 测试未连接时的关节运动
TEST_F(RobotDriverTest, MoveJointWithoutConnection) {
    JointAngles angles;
    angles.joint1 = 10.0;
    angles.joint2 = 20.0;
    angles.joint3 = 30.0;
    
    MotionParams params(50, 50);
    
    bool result = driver->moveJoint(angles, params);
    EXPECT_FALSE(result);
    EXPECT_FALSE(driver->getLastError().empty());
    EXPECT_THAT(driver->getLastError(), HasSubstr("Not connected"));
}

// 测试未连接时的直线运动
TEST_F(RobotDriverTest, MoveLinearWithoutConnection) {
    Position position(100.0, 200.0, 300.0, 0.0, 0.0, 0.0);
    MotionParams params(50, 50);
    
    bool result = driver->moveLinear(position, params);
    EXPECT_FALSE(result);
    EXPECT_FALSE(driver->getLastError().empty());
    EXPECT_THAT(driver->getLastError(), HasSubstr("Not connected"));
}

// 测试未连接时的停止运动
TEST_F(RobotDriverTest, StopWithoutConnection) {
    bool result = driver->stop();
    EXPECT_FALSE(result);
    EXPECT_FALSE(driver->getLastError().empty());
    EXPECT_THAT(driver->getLastError(), HasSubstr("Not connected"));
}

// 测试未连接时的暂停运动
TEST_F(RobotDriverTest, PauseWithoutConnection) {
    bool result = driver->pause();
    EXPECT_FALSE(result);
    EXPECT_FALSE(driver->getLastError().empty());
    EXPECT_THAT(driver->getLastError(), HasSubstr("Not connected"));
}

// 测试未连接时的恢复运动
TEST_F(RobotDriverTest, ResumeWithoutConnection) {
    bool result = driver->resume();
    EXPECT_FALSE(result);
    EXPECT_FALSE(driver->getLastError().empty());
    EXPECT_THAT(driver->getLastError(), HasSubstr("Not connected"));
}

// ========== IO控制测试 ==========

// 测试未连接时的数字输出设置
TEST_F(RobotDriverTest, SetDigitalOutputWithoutConnection) {
    bool result = driver->setDigitalOutput(1, true);
    EXPECT_FALSE(result);
    EXPECT_FALSE(driver->getLastError().empty());
    EXPECT_THAT(driver->getLastError(), HasSubstr("Not connected"));
}

// 测试未连接时的数字输入读取
TEST_F(RobotDriverTest, GetDigitalInputWithoutConnection) {
    bool result = driver->getDigitalInput(1);
    EXPECT_FALSE(result);  // 未连接时返回false
    EXPECT_FALSE(driver->getLastError().empty());
    EXPECT_THAT(driver->getLastError(), HasSubstr("Not connected"));
}

// 测试未连接时的夹爪控制
TEST_F(RobotDriverTest, ControlGripperWithoutConnection) {
    bool result = driver->controlGripper(1, 50);  // 夹取，力度50%
    EXPECT_FALSE(result);
    EXPECT_FALSE(driver->getLastError().empty());
    EXPECT_THAT(driver->getLastError(), HasSubstr("Not connected"));
}

// 测试未连接时的夹爪状态获取
TEST_F(RobotDriverTest, GetGripperStatusWithoutConnection) {
    int status = driver->getGripperStatus();
    EXPECT_EQ(status, -1);  // 未连接时返回-1
    EXPECT_FALSE(driver->getLastError().empty());
    EXPECT_THAT(driver->getLastError(), HasSubstr("Not connected"));
}

// ========== 任务管理测试 ==========

// 测试未连接时的取放任务执行
TEST_F(RobotDriverTest, ExecutePickPlaceWithoutConnection) {
    int taskId = driver->executePickPlace("A01", "B02");
    EXPECT_EQ(taskId, -1);  // 未连接时返回-1
    EXPECT_FALSE(driver->getLastError().empty());
    EXPECT_THAT(driver->getLastError(), HasSubstr("Not connected"));
}

// 测试未连接时的任务状态获取
TEST_F(RobotDriverTest, GetTaskStatusWithoutConnection) {
    int status = driver->getTaskStatus(1);
    EXPECT_EQ(status, -1);  // 未连接时返回-1
    EXPECT_FALSE(driver->getLastError().empty());
    EXPECT_THAT(driver->getLastError(), HasSubstr("Not connected"));
}

// 测试未连接时的任务进度获取
TEST_F(RobotDriverTest, GetTaskProgressWithoutConnection) {
    double progress = driver->getTaskProgress();
    EXPECT_EQ(progress, -1.0);  // 未连接时返回-1.0
    EXPECT_FALSE(driver->getLastError().empty());
    EXPECT_THAT(driver->getLastError(), HasSubstr("Not connected"));
}

// ========== 配置结构测试 ==========

// 测试配置参数验证
TEST_F(RobotDriverTest, ConfigValidation) {
    RobotConfig config;
    
    // 测试默认配置
    EXPECT_EQ(config.ipAddress, "*************");
    EXPECT_EQ(config.port, 502);
    EXPECT_EQ(config.slaveId, 1);
    EXPECT_EQ(config.responseTimeout, 2000);
    EXPECT_EQ(config.maxJointSpeed, 180);
    EXPECT_EQ(config.maxLinearSpeed, 1000);
}

// 测试位置结构
TEST_F(RobotDriverTest, PositionStructure) {
    Position pos;
    
    // 测试默认值
    EXPECT_EQ(pos.x, 0.0);
    EXPECT_EQ(pos.y, 0.0);
    EXPECT_EQ(pos.z, 0.0);
    EXPECT_EQ(pos.rx, 0.0);
    EXPECT_EQ(pos.ry, 0.0);
    EXPECT_EQ(pos.rz, 0.0);
    
    // 测试构造函数
    Position pos2(100.0, 200.0, 300.0, 10.0, 20.0, 30.0);
    EXPECT_EQ(pos2.x, 100.0);
    EXPECT_EQ(pos2.y, 200.0);
    EXPECT_EQ(pos2.z, 300.0);
    EXPECT_EQ(pos2.rx, 10.0);
    EXPECT_EQ(pos2.ry, 20.0);
    EXPECT_EQ(pos2.rz, 30.0);
}

// 测试关节角度结构
TEST_F(RobotDriverTest, JointAnglesStructure) {
    JointAngles angles;
    
    // 测试默认值
    EXPECT_EQ(angles.joint1, 0.0);
    EXPECT_EQ(angles.joint2, 0.0);
    EXPECT_EQ(angles.joint3, 0.0);
    EXPECT_EQ(angles.joint4, 0.0);
    EXPECT_EQ(angles.joint5, 0.0);
    EXPECT_EQ(angles.joint6, 0.0);
}

// 测试运动参数结构
TEST_F(RobotDriverTest, MotionParamsStructure) {
    MotionParams params;
    
    // 测试默认值
    EXPECT_EQ(params.speed, 50);
    EXPECT_EQ(params.acceleration, 50);
    
    // 测试构造函数
    MotionParams params2(80, 60);
    EXPECT_EQ(params2.speed, 80);
    EXPECT_EQ(params2.acceleration, 60);
}

// ========== 枚举值测试 ==========

// 测试RobotStatus枚举
TEST_F(RobotDriverTest, RobotStatusEnum) {
    EXPECT_EQ(static_cast<int>(RobotStatus::DISCONNECTED), 0);
    EXPECT_EQ(static_cast<int>(RobotStatus::IDLE), 1);
    EXPECT_EQ(static_cast<int>(RobotStatus::BUSY), 2);
    EXPECT_EQ(static_cast<int>(RobotStatus::ROBOT_ERROR), 3);
    EXPECT_EQ(static_cast<int>(RobotStatus::EMERGENCY), 4);
}

// 测试MotionMode枚举
TEST_F(RobotDriverTest, MotionModeEnum) {
    EXPECT_EQ(static_cast<int>(MotionMode::MANUAL), 0);
    EXPECT_EQ(static_cast<int>(MotionMode::AUTO), 1);
    EXPECT_EQ(static_cast<int>(MotionMode::TEACH), 2);
    EXPECT_EQ(static_cast<int>(MotionMode::MAINTENANCE), 3);
}

// ========== 位置编码测试 ==========

// 测试有效位置编码
TEST_F(RobotDriverTest, ValidPositionCodes) {
    RobotConfig config;
    driver->initialize(config);

    // 测试工作台位置编码
    int taskId1 = driver->executePickPlace("A01", "B02");
    EXPECT_EQ(taskId1, -1);  // 会因为未连接而失败
    EXPECT_THAT(driver->getLastError(), HasSubstr("Not connected"));

    // 测试货架位置编码
    int taskId2 = driver->executePickPlace("R01", "R06");
    EXPECT_EQ(taskId2, -1);  // 会因为未连接而失败
    EXPECT_THAT(driver->getLastError(), HasSubstr("Not connected"));

    // 测试特殊位置编码
    int taskId3 = driver->executePickPlace("HOME", "SAFE");
    EXPECT_EQ(taskId3, -1);  // 会因为未连接而失败
    EXPECT_THAT(driver->getLastError(), HasSubstr("Not connected"));
}

// 测试无效位置编码
TEST_F(RobotDriverTest, InvalidPositionCodes) {
    RobotConfig config;
    driver->initialize(config);

    // 测试无效位置编码
    int taskId = driver->executePickPlace("INVALID", "UNKNOWN");
    EXPECT_EQ(taskId, -1);
    // 可能因为位置编码无效或未连接而失败
    EXPECT_FALSE(driver->getLastError().empty());
}

// ========== 参数验证测试 ==========

// 测试IO端口范围验证
TEST_F(RobotDriverTest, IOPortValidation) {
    RobotConfig config;
    driver->initialize(config);

    // 测试有效端口范围 (0-15)
    bool result1 = driver->setDigitalOutput(0, true);
    EXPECT_FALSE(result1);  // 会因为未连接而失败
    EXPECT_THAT(driver->getLastError(), HasSubstr("Not connected"));

    bool result2 = driver->setDigitalOutput(15, false);
    EXPECT_FALSE(result2);  // 会因为未连接而失败
    EXPECT_THAT(driver->getLastError(), HasSubstr("Not connected"));

    // 测试无效端口（如果有验证的话）
    bool input1 = driver->getDigitalInput(0);
    EXPECT_FALSE(input1);  // 会因为未连接而失败

    bool input2 = driver->getDigitalInput(15);
    EXPECT_FALSE(input2);  // 会因为未连接而失败
}

// 测试夹爪参数验证
TEST_F(RobotDriverTest, GripperParameterValidation) {
    RobotConfig config;
    driver->initialize(config);

    // 测试有效夹爪指令 (0=释放, 1=夹取, 2=停止)
    bool result1 = driver->controlGripper(0, 50);  // 释放
    EXPECT_FALSE(result1);  // 会因为未连接而失败
    EXPECT_THAT(driver->getLastError(), HasSubstr("Not connected"));

    bool result2 = driver->controlGripper(1, 100); // 夹取，最大力度
    EXPECT_FALSE(result2);  // 会因为未连接而失败
    EXPECT_THAT(driver->getLastError(), HasSubstr("Not connected"));

    bool result3 = driver->controlGripper(2, 1);   // 停止，最小力度
    EXPECT_FALSE(result3);  // 会因为未连接而失败
    EXPECT_THAT(driver->getLastError(), HasSubstr("Not connected"));
}

// 测试运动参数边界值
TEST_F(RobotDriverTest, MotionParameterBoundaries) {
    // 测试速度和加速度的边界值
    MotionParams params1(1, 1);     // 最小值
    EXPECT_EQ(params1.speed, 1);
    EXPECT_EQ(params1.acceleration, 1);

    MotionParams params2(100, 100); // 最大值
    EXPECT_EQ(params2.speed, 100);
    EXPECT_EQ(params2.acceleration, 100);

    // 测试超出范围的值（如果有验证的话）
    MotionParams params3(0, 0);     // 低于最小值
    MotionParams params4(150, 150); // 高于最大值
}

// ========== 错误处理测试 ==========

// 测试错误信息的持久性
TEST_F(RobotDriverTest, ErrorPersistence) {
    // 触发一个错误
    driver->home();  // 未连接时调用
    std::string firstError = driver->getLastError();
    EXPECT_FALSE(firstError.empty());

    // 再次获取错误信息，应该保持不变
    std::string secondError = driver->getLastError();
    EXPECT_EQ(firstError, secondError);

    // 触发另一个错误
    driver->stop();  // 未连接时调用
    std::string thirdError = driver->getLastError();
    EXPECT_NE(firstError, thirdError);  // 错误信息应该更新
}

// ========== 状态转换测试 ==========

// 测试状态转换逻辑
TEST_F(RobotDriverTest, StatusTransitions) {
    RobotConfig config;
    driver->initialize(config);

    // 初始状态应该是DISCONNECTED
    EXPECT_EQ(driver->getStatus(), RobotStatus::DISCONNECTED);

    // 测试状态回调中的状态转换
    std::vector<RobotStatus> statusHistory;
    driver->setStatusCallback([&](RobotStatus status, const std::string& message) {
        statusHistory.push_back(status);
    });

    // 重新初始化以触发状态回调
    driver->initialize(config);

    // 验证状态历史
    EXPECT_FALSE(statusHistory.empty());
    EXPECT_EQ(statusHistory.back(), RobotStatus::DISCONNECTED);
}

// ========== 复杂场景测试 ==========

// 测试复杂的运动序列
TEST_F(RobotDriverTest, ComplexMotionSequence) {
    RobotConfig config;
    driver->initialize(config);

    // 创建复杂的运动序列
    JointAngles angles1;
    angles1.joint1 = 10.0;
    angles1.joint2 = 20.0;
    angles1.joint3 = 30.0;

    Position position1(100.0, 200.0, 300.0, 0.0, 0.0, 0.0);
    MotionParams params(75, 60);

    // 尝试执行运动序列
    bool result1 = driver->home();
    EXPECT_FALSE(result1);  // 会因为未连接而失败

    bool result2 = driver->moveJoint(angles1, params);
    EXPECT_FALSE(result2);  // 会因为未连接而失败

    bool result3 = driver->moveLinear(position1, params);
    EXPECT_FALSE(result3);  // 会因为未连接而失败

    // 所有操作都应该因为未连接而失败
    EXPECT_THAT(driver->getLastError(), HasSubstr("Not connected"));
}

// 测试取放任务的完整流程
TEST_F(RobotDriverTest, CompletePickPlaceTask) {
    RobotConfig config;
    driver->initialize(config);

    // 执行取放任务
    int taskId = driver->executePickPlace("A01", "B02");
    EXPECT_EQ(taskId, -1);  // 会因为未连接而失败

    // 查询任务状态
    int status = driver->getTaskStatus(taskId);
    EXPECT_EQ(status, -1);  // 会因为未连接而失败

    // 查询任务进度
    double progress = driver->getTaskProgress();
    EXPECT_EQ(progress, -1.0);  // 会因为未连接而失败

    EXPECT_THAT(driver->getLastError(), HasSubstr("Not connected"));
}

// ========== 性能测试 ==========

// 性能测试：测试多次操作的性能
TEST_F(RobotDriverTest, PerformanceTest) {
    RobotConfig config;
    config.ipAddress = "*************";
    config.port = 502;
    config.slaveId = 1;

    driver->initialize(config);

    auto start = std::chrono::high_resolution_clock::now();

    // 执行多次操作
    for (int i = 0; i < 100; ++i) {
        driver->getStatus();
        driver->getLastError();
        driver->getErrorCode();
    }

    auto end = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);

    // 确保操作在合理时间内完成（这里设置为1秒）
    EXPECT_LT(duration.count(), 1000);
}

// ========== 线程安全测试 ==========

// 线程安全测试
TEST_F(RobotDriverTest, ThreadSafetyTest) {
    RobotConfig config;
    config.ipAddress = "*************";
    config.port = 502;
    config.slaveId = 1;

    driver->initialize(config);

    std::vector<std::thread> threads;
    std::atomic<int> successCount{0};
    std::atomic<int> errorCount{0};

    // 创建多个线程同时访问驱动
    for (int i = 0; i < 10; ++i) {
        threads.emplace_back([&]() {
            for (int j = 0; j < 10; ++j) {
                try {
                    driver->getStatus();
                    driver->getLastError();
                    driver->getCurrentPosition();
                    driver->getCurrentJointAngles();
                    successCount++;
                } catch (...) {
                    errorCount++;
                }
                std::this_thread::sleep_for(std::chrono::milliseconds(1));
            }
        });
    }

    // 等待所有线程完成
    for (auto& thread : threads) {
        thread.join();
    }

    // 验证没有崩溃，所有操作都完成了
    EXPECT_EQ(successCount + errorCount, 100);
}

// ========== 并发状态测试 ==========

// 测试并发状态查询
TEST_F(RobotDriverTest, ConcurrentStatusQueries) {
    RobotConfig config;
    driver->initialize(config);

    std::vector<std::thread> threads;
    std::atomic<bool> allStatusConsistent{true};

    // 创建多个线程同时查询状态
    for (int i = 0; i < 5; ++i) {
        threads.emplace_back([&]() {
            RobotStatus lastStatus = driver->getStatus();
            for (int j = 0; j < 20; ++j) {
                RobotStatus currentStatus = driver->getStatus();
                // 在没有状态变化的情况下，状态应该保持一致
                if (currentStatus != lastStatus && lastStatus == RobotStatus::DISCONNECTED) {
                    allStatusConsistent = false;
                }
                lastStatus = currentStatus;
                std::this_thread::sleep_for(std::chrono::milliseconds(1));
            }
        });
    }

    // 等待所有线程完成
    for (auto& thread : threads) {
        thread.join();
    }

    // 在测试环境中，状态应该保持一致
    EXPECT_TRUE(allStatusConsistent);
}

// ========== 内存和资源测试 ==========

// 测试多次初始化和断开连接
TEST_F(RobotDriverTest, MultipleInitializeDisconnect) {
    RobotConfig config;
    config.ipAddress = "*************";
    config.port = 502;
    config.slaveId = 1;

    // 多次初始化和断开连接，检查是否有内存泄漏
    for (int i = 0; i < 10; ++i) {
        bool result = driver->initialize(config);
        EXPECT_TRUE(result || !driver->getLastError().empty());

        driver->disconnect();
        EXPECT_EQ(driver->getStatus(), RobotStatus::DISCONNECTED);
    }
}

// ========== 边界条件测试 ==========

// 测试极限位置值
TEST_F(RobotDriverTest, ExtremePosisionValues) {
    // 测试极大值
    Position pos1(999999.0, 999999.0, 999999.0, 360.0, 360.0, 360.0);
    EXPECT_EQ(pos1.x, 999999.0);
    EXPECT_EQ(pos1.rx, 360.0);

    // 测试极小值
    Position pos2(-999999.0, -999999.0, -999999.0, -360.0, -360.0, -360.0);
    EXPECT_EQ(pos2.x, -999999.0);
    EXPECT_EQ(pos2.rx, -360.0);

    // 测试零值
    Position pos3(0.0, 0.0, 0.0, 0.0, 0.0, 0.0);
    EXPECT_EQ(pos3.x, 0.0);
    EXPECT_EQ(pos3.rx, 0.0);
}

// 测试极限关节角度值
TEST_F(RobotDriverTest, ExtremeJointAngleValues) {
    JointAngles angles;

    // 设置极限值
    angles.joint1 = 180.0;
    angles.joint2 = -180.0;
    angles.joint3 = 360.0;
    angles.joint4 = -360.0;
    angles.joint5 = 0.0;
    angles.joint6 = 0.1;

    EXPECT_EQ(angles.joint1, 180.0);
    EXPECT_EQ(angles.joint2, -180.0);
    EXPECT_EQ(angles.joint3, 360.0);
    EXPECT_EQ(angles.joint4, -360.0);
    EXPECT_EQ(angles.joint5, 0.0);
    EXPECT_EQ(angles.joint6, 0.1);
}

// ========== 配置完整性测试 ==========

// 测试配置的完整性
TEST_F(RobotDriverTest, ConfigCompleteness) {
    RobotConfig config;
    config.ipAddress = "********";
    config.port = 1502;
    config.slaveId = 5;
    config.responseTimeout = 5000;
    config.maxJointSpeed = 90;
    config.maxLinearSpeed = 500;

    bool result = driver->initialize(config);
    EXPECT_TRUE(result || !driver->getLastError().empty());

    // 验证配置是否正确设置
    // 由于没有公共接口获取配置，我们通过其他方式验证
    EXPECT_EQ(driver->getStatus(), RobotStatus::DISCONNECTED);
}

// ========== 回调机制测试 ==========

// 测试多个状态回调
TEST_F(RobotDriverTest, MultipleStatusCallbacks) {
    std::vector<RobotStatus> statusHistory1;
    std::vector<RobotStatus> statusHistory2;

    // 设置第一个回调
    driver->setStatusCallback([&](RobotStatus status, const std::string& message) {
        statusHistory1.push_back(status);
    });

    RobotConfig config;
    driver->initialize(config);

    // 设置第二个回调（应该覆盖第一个）
    driver->setStatusCallback([&](RobotStatus status, const std::string& message) {
        statusHistory2.push_back(status);
    });

    // 重新初始化以触发回调
    driver->initialize(config);

    // 只有第二个回调应该被调用
    EXPECT_TRUE(statusHistory1.size() >= 1);  // 第一次初始化时被调用
    EXPECT_TRUE(statusHistory2.size() >= 1);  // 第二次初始化时被调用
}

int main(int argc, char** argv) {
    ::testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}
