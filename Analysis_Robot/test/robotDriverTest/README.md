# RobotDriver 测试

这个目录包含了对 `RobotDriver` 类的全面测试套件。

## 测试覆盖范围

### 基础功能测试
- 构造函数和析构函数
- 初始化和配置验证
- 连接和断开连接
- 状态管理和回调

### 状态查询测试
- 机器人状态获取
- 当前TCP位置获取
- 关节角度获取
- 错误代码获取

### 运动控制测试
- 回原点操作
- 关节运动控制
- 直线运动控制
- 停止、暂停、恢复操作

### IO控制测试
- 数字输出设置 (端口0-15)
- 数字输入读取 (端口0-15)
- 夹爪控制 (释放/夹取/停止)
- 夹爪状态获取

### 任务管理测试
- 取放任务执行
- 任务状态查询
- 任务进度获取

### 位置编码测试
- 工作台位置编码 (A01-C03)
- 货架位置编码 (R01-R06)
- 特殊位置编码 (HOME, SAFE, MAINTENANCE)
- 无效位置编码处理

### 参数验证测试
- IO端口范围验证 (0-15)
- 夹爪参数验证 (指令0-2, 力度1-100%)
- 运动参数边界值 (速度1-100%, 加速度1-100%)

### 性能和稳定性测试
- 性能测试
- 线程安全测试
- 并发状态查询测试
- 内存和资源管理测试

## 构建和运行

### 前提条件
- CMake 3.10+
- Google Test (gtest)
- Google Mock (gmock)
- libmodbus
- 其他项目依赖

### 构建测试
```bash
# 在项目根目录下
mkdir build
cd build
cmake ..
make robotDriverTest
```

### 运行测试
```bash
# 运行所有测试
./test/robotDriverTest/robotDriverTest

# 运行特定测试
./test/robotDriverTest/robotDriverTest --gtest_filter="*InitializeWithValidConfig*"

# 运行测试并显示详细输出
./test/robotDriverTest/robotDriverTest --gtest_output=xml:test_results.xml

# 运行性能测试
./test/robotDriverTest/robotDriverTest --gtest_filter="*PerformanceTest*"

# 运行线程安全测试
./test/robotDriverTest/robotDriverTest --gtest_filter="*ThreadSafetyTest*"
```

## 测试说明

### 模拟环境
由于这些测试不依赖实际的机器人硬件，大部分测试会在以下情况下运行：
- 没有实际的网络连接到机器人
- 没有实际的MODBUS TCP设备
- 测试主要验证参数验证、错误处理和状态管理逻辑

### 实际硬件测试
如果需要在实际机器人上测试，需要：
1. 连接6轴机械臂设备
2. 配置正确的网络参数
3. 修改测试中的配置参数以匹配实际设备
4. 确保机器人处于安全状态

### 测试结果解释
- **PASS**: 测试通过，功能正常
- **FAIL**: 测试失败，可能存在问题
- 大部分连接相关的测试会因为没有实际设备而失败，这是正常的

## 测试用例详情

### 基础功能测试
- `InitializeWithValidConfig`: 测试有效配置的初始化
- `InitializeWithInvalidConfig`: 测试无效配置的处理
- `StatusCallback`: 测试状态回调机制
- `ConnectionStatus`: 测试连接状态检查

### 状态查询测试
- `GetCurrentPositionWithoutConnection`: 测试未连接时的位置获取
- `GetCurrentJointAnglesWithoutConnection`: 测试未连接时的关节角度获取
- `GetErrorCodeWithoutConnection`: 测试未连接时的错误代码获取

### 运动控制测试
- `HomeWithoutConnection`: 测试未连接时的回原点
- `MoveJointWithoutConnection`: 测试未连接时的关节运动
- `MoveLinearWithoutConnection`: 测试未连接时的直线运动

### IO控制测试
- `SetDigitalOutputWithoutConnection`: 测试未连接时的数字输出设置
- `GetDigitalInputWithoutConnection`: 测试未连接时的数字输入读取
- `ControlGripperWithoutConnection`: 测试未连接时的夹爪控制

### 位置编码测试
- `ValidPositionCodes`: 测试有效位置编码
- `InvalidPositionCodes`: 测试无效位置编码处理

### 性能测试
- `PerformanceTest`: 测试多次操作的性能
- `ThreadSafetyTest`: 测试线程安全性
- `ConcurrentStatusQueries`: 测试并发状态查询

## 机器人位置编码

### 工作台位置
- A01, A02, A03 - A工作台
- B01, B02, B03 - B工作台  
- C01, C02, C03 - C工作台

### 货架位置
- R01, R02, R03, R04, R05, R06 - 货架位置

### 特殊位置
- HOME - 原点位置
- SAFE - 安全位置
- MAINTENANCE - 维护位置

## 扩展测试

如果需要添加更多测试，可以：
1. 在 `robotDriverTest.cpp` 中添加新的测试用例
2. 使用 Google Test 的 `TEST_F` 宏
3. 遵循现有的测试命名约定
4. 添加新的位置编码测试

## 故障排除

### 常见问题
1. **编译错误**: 检查依赖库是否正确安装
2. **链接错误**: 确保 CMakeLists.txt 中的依赖配置正确
3. **运行时错误**: 检查网络连接和防火墙设置

### 调试技巧
- 使用 `--gtest_break_on_failure` 在失败时中断
- 使用 `--gtest_repeat=N` 重复运行测试
- 使用 `--gtest_shuffle` 随机化测试顺序
- 使用 `--gtest_filter="*ThreadSafety*"` 运行特定类型的测试
