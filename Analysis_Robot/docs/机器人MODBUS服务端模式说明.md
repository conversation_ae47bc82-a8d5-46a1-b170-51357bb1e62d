# 机器人MODBUS服务端模式说明

## 概述

本文档说明了机器人驱动器的新架构：**软件作为MODBUS服务端，机器人作为MODBUS客户端**。

## 架构变更

### 原架构（客户端模式）
- 软件作为MODBUS客户端
- 机器人作为MODBUS服务端
- 软件主动连接机器人
- 软件主动发起读写请求

### 新架构（服务端模式）
- **软件作为MODBUS服务端**
- **机器人作为MODBUS客户端**
- 机器人主动连接软件
- 机器人主动发起读写请求

## 优势

### 1. 连接稳定性
- 服务端可以主动管理连接状态
- 避免客户端重连的复杂性
- 更好的错误处理和恢复机制

### 2. 数据一致性
- 服务端维护统一的寄存器映射
- 避免数据同步问题
- 实时状态更新

### 3. 扩展性
- 支持多个机器人同时连接
- 统一的寄存器地址空间
- 便于添加新功能

## 配置变更

### 原配置格式
```json
{
  "robot1": {
    "type": "robot",
    "config": {
      "ipAddress": "*************",  // 机器人IP
      "port": 502,
      "slaveId": 1,
      "responseTimeout": 2000
    }
  }
}
```

### 新配置格式
```json
{
  "robot1": {
    "type": "robot",
    "config": {
      "ipAddress": "0.0.0.0",        // 服务端监听IP (0.0.0.0表示所有接口)
      "port": 502,                   // 服务端监听端口
      "responseTimeout": 2000
    }
  }
}
```

## 寄存器映射

### 系统状态寄存器 (0x0000-0x000F)
| 地址 | 名称 | 类型 | 描述 |
|------|------|------|------|
| 0x0000 | SYSTEM_STATUS | R | 系统状态 |
| 0x0001 | ERROR_CODE | R | 错误代码 |
| 0x0002 | ROBOT_MODE | R | 机器人模式 |
| 0x0003 | SAFETY_STATUS | R | 安全状态 |
| 0x0004 | CONNECTION_STATUS | R | 连接状态 |
| 0x0005 | HEARTBEAT | R | 心跳计数器 |

### 位置反馈寄存器 (0x0010-0x002F)
| 地址 | 名称 | 类型 | 描述 |
|------|------|------|------|
| 0x0010 | CURRENT_X | R | 当前X位置 (32位) |
| 0x0012 | CURRENT_Y | R | 当前Y位置 (32位) |
| 0x0014 | CURRENT_Z | R | 当前Z位置 (32位) |
| 0x0016 | CURRENT_RX | R | 当前RX角度 (32位) |
| 0x0018 | CURRENT_RY | R | 当前RY角度 (32位) |
| 0x001A | CURRENT_RZ | R | 当前RZ角度 (32位) |
| 0x001C | CURRENT_J1 | R | 当前关节1角度 (32位) |
| 0x001E | CURRENT_J2 | R | 当前关节2角度 (32位) |
| 0x0020 | CURRENT_J3 | R | 当前关节3角度 (32位) |
| 0x0022 | CURRENT_J4 | R | 当前关节4角度 (32位) |
| 0x0024 | CURRENT_J5 | R | 当前关节5角度 (32位) |
| 0x0026 | CURRENT_J6 | R | 当前关节6角度 (32位) |

### 运动控制寄存器 (0x0030-0x004F)
| 地址 | 名称 | 类型 | 描述 |
|------|------|------|------|
| 0x0030 | MOTION_CMD | W | 运动指令 |
| 0x0031 | MOTION_STATUS | R | 运动状态 |
| 0x0032 | TARGET_X | W | 目标X位置 (32位) |
| 0x0034 | TARGET_Y | W | 目标Y位置 (32位) |
| 0x0036 | TARGET_Z | W | 目标Z位置 (32位) |
| 0x0038 | TARGET_RX | W | 目标RX角度 (32位) |
| 0x003A | TARGET_RY | W | 目标RY角度 (32位) |
| 0x003C | TARGET_RZ | W | 目标RZ角度 (32位) |
| 0x003E | MOTION_SPEED | W | 运动速度 |
| 0x003F | MOTION_ACCEL | W | 运动加速度 |
| 0x0040 | TARGET_J1 | W | 目标关节1角度 (32位) |
| 0x0042 | TARGET_J2 | W | 目标关节2角度 (32位) |
| 0x0044 | TARGET_J3 | W | 目标关节3角度 (32位) |
| 0x0046 | TARGET_J4 | W | 目标关节4角度 (32位) |
| 0x0048 | TARGET_J5 | W | 目标关节5角度 (32位) |
| 0x004A | TARGET_J6 | W | 目标关节6角度 (32位) |

### IO控制寄存器 (0x0050-0x005F)
| 地址 | 名称 | 类型 | 描述 |
|------|------|------|------|
| 0x0050 | DIGITAL_OUT | R/W | 数字输出 |
| 0x0051 | DIGITAL_IN | R | 数字输入 |
| 0x0052 | GRIPPER_CMD | W | 夹爪指令 |
| 0x0053 | GRIPPER_STATUS | R | 夹爪状态 |
| 0x0054 | GRIPPER_FORCE | W | 夹爪力度 |

### 任务管理寄存器 (0x0070-0x007F)
| 地址 | 名称 | 类型 | 描述 |
|------|------|------|------|
| 0x0070 | TASK_CMD | W | 任务指令 |
| 0x0071 | TASK_STATUS | R | 任务状态 |
| 0x0072 | TASK_PROGRESS | R | 任务进度 (32位) |
| 0x0074 | PICK_PLACE_CMD | W | 取放指令 |
| 0x0075 | PICK_PLACE_STATUS | R | 取放状态 |
| 0x0076 | SOURCE_POSITION | W | 源位置编码 |
| 0x0077 | DEST_POSITION | W | 目标位置编码 |

## 运动指令编码

### MOTION_CMD (0x0030)
- 1: HOME - 回原点
- 2: MOVEJ - 关节运动
- 3: MOVEL - 直线运动
- 4: STOP - 停止
- 5: PAUSE - 暂停
- 6: RESUME - 恢复

### MOTION_STATUS (0x0031)
- 0: IDLE - 空闲
- 1: RUNNING - 运行中
- 2: COMPLETED - 完成
- 3: ERROR - 错误

## 使用示例

### 启动服务器
```cpp
RobotDriver driver;
RobotConfig config;
config.ipAddress = "0.0.0.0";  // 监听所有接口
config.port = 502;

driver.initialize(config);
driver.connect();  // 启动服务器，等待机器人连接
```

### 检查连接状态
```cpp
if (driver.isConnected()) {
    // 机器人已连接
    Position pos = driver.getCurrentPosition();
    // ...
}
```

## 机器人端配置

机器人需要配置为MODBUS客户端模式，连接到软件的IP地址和端口。

### 示例配置
- 服务器IP: 软件运行的计算机IP
- 服务器端口: 502
- 从站ID: 1
- 连接超时: 5秒
- 响应超时: 2秒

## 故障排除

### 1. 连接问题
- 检查防火墙设置
- 确认端口未被占用
- 验证网络连通性

### 2. 数据同步问题
- 检查寄存器地址映射
- 验证数据类型和字节序
- 确认读写权限

### 3. 性能问题
- 调整心跳间隔
- 优化寄存器访问频率
- 检查网络延迟

## 迁移指南

### 从客户端模式迁移到服务端模式

1. **更新配置文件**
   - 修改ipAddress为服务端监听地址
   - 移除slaveId配置

2. **更新机器人配置**
   - 配置机器人为MODBUS客户端
   - 设置连接到软件的IP和端口

3. **测试连接**
   - 启动软件服务端
   - 启动机器人客户端
   - 验证连接和数据交换

4. **功能验证**
   - 测试所有运动指令
   - 验证IO控制
   - 检查任务管理功能
