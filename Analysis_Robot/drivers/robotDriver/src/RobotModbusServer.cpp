#include "RobotModbusServer.h"
#include <iostream>
#include <chrono>
#include <cstring>
#ifdef _WIN32
#include <winsock2.h>
#else
#include <unistd.h>
#endif

namespace AnalysisRobot {
namespace Robot {

RobotModbusServer::RobotModbusServer()
    : m_ctx(nullptr)
    , m_mapping(nullptr)
    , m_port(502)
    , m_serverSocket(-1)
    , m_clientSocket(-1)
    , m_running(false)
    , m_clientConnected(false)
    , m_heartbeatCounter(0) {
}

RobotModbusServer::~RobotModbusServer() {
    stop();
    cleanup();
}

bool RobotModbusServer::initialize(const std::string& ip, int port) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    m_ip = ip;
    m_port = port;
    
    // 创建MODBUS TCP上下文
    m_ctx = modbus_new_tcp(ip.c_str(), port);
    if (m_ctx == nullptr) {
        setError("Failed to create MODBUS TCP context: " + std::string(modbus_strerror(errno)));
        return false;
    }
    
    // 创建寄存器映射 (保持寄存器: 1000个)
    m_mapping = modbus_mapping_new(0, 0, 1000, 0);
    if (m_mapping == nullptr) {
        setError("Failed to create MODBUS mapping: " + std::string(modbus_strerror(errno)));
        modbus_free(m_ctx);
        m_ctx = nullptr;
        return false;
    }
    
    // 初始化寄存器默认值
    memset(m_mapping->tab_registers, 0, sizeof(uint16_t) * m_mapping->nb_registers);
    
    // 设置初始状态
    writeRegister(ModbusRegisters::SYSTEM_STATUS, 1);  // 系统就绪
    writeRegister(ModbusRegisters::CONNECTION_STATUS, 0);  // 未连接
    
    return true;
}

bool RobotModbusServer::start() {
    if (m_running) {
        return true;
    }
    
    if (m_ctx == nullptr) {
        setError("MODBUS context not initialized");
        return false;
    }
    
    // 创建服务器套接字
    m_serverSocket = modbus_tcp_listen(m_ctx, 1);
    if (m_serverSocket == -1) {
        setError("Failed to create server socket: " + std::string(modbus_strerror(errno)));
        return false;
    }
    
    m_running = true;
    
    // 启动服务器线程
    m_serverThread = std::thread(&RobotModbusServer::serverLoop, this);
    
    // 启动心跳线程
    m_heartbeatThread = std::thread(&RobotModbusServer::heartbeatLoop, this);
    
    return true;
}

void RobotModbusServer::stop() {
    m_running = false;
    
    // 关闭客户端连接
    if (m_clientSocket != -1) {
        modbus_close(m_ctx);
        m_clientSocket = -1;
    }
    
    // 关闭服务器套接字
    if (m_serverSocket != -1) {
#ifdef _WIN32
        closesocket(m_serverSocket);
#else
        close(m_serverSocket);
#endif
        m_serverSocket = -1;
    }
    
    // 等待线程结束
    if (m_serverThread.joinable()) {
        m_serverThread.join();
    }
    
    if (m_heartbeatThread.joinable()) {
        m_heartbeatThread.join();
    }
    
    m_clientConnected = false;
    
    // 通知连接状态变化
    if (m_connectionCallback) {
        m_connectionCallback(false);
    }
}

bool RobotModbusServer::isClientConnected() const {
    return m_clientConnected;
}

uint16_t RobotModbusServer::readRegister(int address) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (m_mapping == nullptr || address < 0 || address >= m_mapping->nb_registers) {
        return 0;
    }
    
    return m_mapping->tab_registers[address];
}

void RobotModbusServer::writeRegister(int address, uint16_t value) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (m_mapping == nullptr || address < 0 || address >= m_mapping->nb_registers) {
        return;
    }
    
    m_mapping->tab_registers[address] = value;
}

int32_t RobotModbusServer::readInt32(int address) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (m_mapping == nullptr || address < 0 || address + 1 >= m_mapping->nb_registers) {
        return 0;
    }
    
    uint16_t high = m_mapping->tab_registers[address];
    uint16_t low = m_mapping->tab_registers[address + 1];
    
    return (static_cast<int32_t>(high) << 16) | low;
}

void RobotModbusServer::writeInt32(int address, int32_t value) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (m_mapping == nullptr || address < 0 || address + 1 >= m_mapping->nb_registers) {
        return;
    }
    
    m_mapping->tab_registers[address] = static_cast<uint16_t>((value >> 16) & 0xFFFF);
    m_mapping->tab_registers[address + 1] = static_cast<uint16_t>(value & 0xFFFF);
}

void RobotModbusServer::setRegisterUpdateCallback(RegisterUpdateCallback callback) {
    std::lock_guard<std::mutex> lock(m_mutex);
    m_registerCallback = callback;
}

void RobotModbusServer::setConnectionCallback(ConnectionCallback callback) {
    std::lock_guard<std::mutex> lock(m_mutex);
    m_connectionCallback = callback;
}

std::string RobotModbusServer::getLastError() const {
    std::lock_guard<std::mutex> lock(m_mutex);
    return m_lastError;
}

void RobotModbusServer::serverLoop() {
    while (m_running) {
        try {
            // 等待客户端连接
            m_clientSocket = modbus_tcp_accept(m_ctx, &m_serverSocket);
            if (m_clientSocket == -1) {
                if (m_running) {
                    std::this_thread::sleep_for(std::chrono::milliseconds(100));
                }
                continue;
            }
            
            m_clientConnected = true;
            writeRegister(ModbusRegisters::CONNECTION_STATUS, 1);
            
            // 通知连接状态变化
            if (m_connectionCallback) {
                m_connectionCallback(true);
            }
            
            handleClientConnection();
            
        } catch (const std::exception& e) {
            setError("Server loop exception: " + std::string(e.what()));
        }
    }
}

void RobotModbusServer::heartbeatLoop() {
    while (m_running) {
        try {
            // 更新心跳计数器
            m_heartbeatCounter++;
            writeRegister(ModbusRegisters::HEARTBEAT, m_heartbeatCounter);
            
            std::this_thread::sleep_for(std::chrono::milliseconds(500));
            
        } catch (const std::exception& e) {
            setError("Heartbeat loop exception: " + std::string(e.what()));
        }
    }
}

void RobotModbusServer::handleClientConnection() {
    uint8_t query[MODBUS_TCP_MAX_ADU_LENGTH];
    
    while (m_running && m_clientConnected) {
        try {
            // 接收客户端请求
            int rc = modbus_receive(m_ctx, query);
            
            if (rc > 0) {
                // 处理请求并发送响应
                int response_length = modbus_reply(m_ctx, query, rc, m_mapping);
                
                if (response_length == -1) {
                    setError("Failed to send response: " + std::string(modbus_strerror(errno)));
                    break;
                }
                
                // 检查是否有寄存器更新
                if (m_registerCallback) {
                    // 这里可以添加寄存器变化检测逻辑
                }
                
            } else if (rc == -1) {
                // 连接断开
                break;
            }
            
        } catch (const std::exception& e) {
            setError("Client handling exception: " + std::string(e.what()));
            break;
        }
    }
    
    // 客户端断开连接
    m_clientConnected = false;
    writeRegister(ModbusRegisters::CONNECTION_STATUS, 0);
    
    if (m_clientSocket != -1) {
        modbus_close(m_ctx);
        m_clientSocket = -1;
    }
    
    // 通知连接状态变化
    if (m_connectionCallback) {
        m_connectionCallback(false);
    }
}

void RobotModbusServer::setError(const std::string& error) {
    m_lastError = error;
    std::cerr << "RobotModbusServer Error: " << error << std::endl;
}

void RobotModbusServer::cleanup() {
    if (m_mapping) {
        modbus_mapping_free(m_mapping);
        m_mapping = nullptr;
    }
    
    if (m_ctx) {
        modbus_free(m_ctx);
        m_ctx = nullptr;
    }
}

} // namespace Robot
} // namespace AnalysisRobot
