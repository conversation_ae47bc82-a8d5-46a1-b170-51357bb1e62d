#include "RobotDriver.h"
#include "RobotModbusServer.h"
#include <iostream>
#include <iomanip>
#include <sstream>
#include <cstring>
#include <errno.h>
#include <chrono>
#include <thread>

namespace AnalysisRobot {
namespace Robot {

using namespace ModbusRegisters;

// 位置编码映射表
std::map<std::string, uint16_t> RobotDriver::s_positionCodes = {
    // 工作台位置
    {"A01", 0x0A01}, {"A02", 0x0A02}, {"A03", 0x0A03},
    {"B01", 0x0B01}, {"B02", 0x0B02}, {"B03", 0x0B03},
    {"C01", 0x0C01}, {"C02", 0x0C02}, {"C03", 0x0C03},
    
    // 货架位置
    {"R01", 0x0101}, {"R02", 0x0102}, {"R03", 0x0103},
    {"R04", 0x0104}, {"R05", 0x0105}, {"R06", 0x0106},
    
    // 特殊位置
    {"HOME", 0x0000},           // 原点位置
    {"SAFE", 0x0001},           // 安全位置
    {"MAINTENANCE", 0x0002}     // 维护位置
};

RobotDriver::RobotDriver()
    : m_modbusServer(std::make_shared<RobotModbusServer>())
    , m_status(RobotStatus::DISCONNECTED)
    , m_statusCallback(nullptr) {
}

RobotDriver::~RobotDriver() {
    disconnect();
}

bool RobotDriver::initialize(const RobotConfig& config) {
    std::lock_guard<std::mutex> lock(m_mutex);

    m_config = config;

    // 初始化MODBUS服务器
    if (!m_modbusServer->initialize(m_config.ipAddress, m_config.port)) {
        setError("Failed to initialize MODBUS server: " + m_modbusServer->getLastError());
        return false;
    }

    // 设置连接状态回调
    m_modbusServer->setConnectionCallback([this](bool connected) {
        if (connected) {
            setStatus(RobotStatus::IDLE, "Robot connected");
        } else {
            setStatus(RobotStatus::DISCONNECTED, "Robot disconnected");
        }
    });

    // 设置寄存器更新回调
    m_modbusServer->setRegisterUpdateCallback([this](int address, uint16_t value) {
        handleRegisterUpdate(address, value);
    });

    setStatus(RobotStatus::DISCONNECTED, "Initialized successfully");
    return true;
}

bool RobotDriver::connect() {
    std::lock_guard<std::mutex> lock(m_mutex);

    if (!m_modbusServer) {
        setError("MODBUS server not initialized");
        return false;
    }

    // 启动MODBUS服务器，等待机器人连接
    if (!m_modbusServer->start()) {
        setError("Failed to start MODBUS server: " + m_modbusServer->getLastError());
        return false;
    }

    setStatus(RobotStatus::DISCONNECTED, "Server started, waiting for robot connection");
    return true;
}

void RobotDriver::disconnect() {
    std::lock_guard<std::mutex> lock(m_mutex);

    if (m_modbusServer) {
        m_modbusServer->stop();
    }

    setStatus(RobotStatus::DISCONNECTED, "Disconnected");
}

bool RobotDriver::isConnected() const {
    return m_modbusServer && m_modbusServer->isClientConnected();
}

RobotStatus RobotDriver::getStatus() const {
    return m_status.load();
}

Position RobotDriver::getCurrentPosition() {
    Position pos;
    
    if (!isConnected()) {
        setError("Not connected to robot");
        return pos;
    }
    
    // 读取TCP位置
    int32_t x = readInt32(CURRENT_X);   // 当前X位置
    int32_t y = readInt32(CURRENT_Y);   // 当前Y位置
    int32_t z = readInt32(CURRENT_Z);   // 当前Z位置
    int32_t rx = readInt32(CURRENT_RX); // 当前RX角度
    int32_t ry = readInt32(CURRENT_RY); // 当前RY角度
    int32_t rz = readInt32(CURRENT_RZ); // 当前RZ角度
    
    // 转换单位：0.1mm -> mm, 0.01度 -> 度
    pos.x = x / 10.0;
    pos.y = y / 10.0;
    pos.z = z / 10.0;
    pos.rx = rx / 100.0;
    pos.ry = ry / 100.0;
    pos.rz = rz / 100.0;
    
    return pos;
}

JointAngles RobotDriver::getCurrentJointAngles() {
    JointAngles angles;
    
    if (!isConnected()) {
        setError("Not connected to robot");
        return angles;
    }
    
    // 读取关节角度
    int32_t j1 = readInt32(CURRENT_J1);  // 当前关节1角度
    int32_t j2 = readInt32(CURRENT_J2);  // 当前关节2角度
    int32_t j3 = readInt32(CURRENT_J3);  // 当前关节3角度
    int32_t j4 = readInt32(CURRENT_J4);  // 当前关节4角度
    int32_t j5 = readInt32(CURRENT_J5);  // 当前关节5角度
    int32_t j6 = readInt32(CURRENT_J6);  // 当前关节6角度
    
    // 转换单位：0.01度 -> 度
    angles.joint1 = j1 / 100.0;
    angles.joint2 = j2 / 100.0;
    angles.joint3 = j3 / 100.0;
    angles.joint4 = j4 / 100.0;
    angles.joint5 = j5 / 100.0;
    angles.joint6 = j6 / 100.0;
    
    return angles;
}

int RobotDriver::getErrorCode() {
    if (!isConnected()) {
        return -1;
    }
    
    return readRegister(ERROR_CODE);
}

bool RobotDriver::home() {
    if (!isConnected()) {
        setError("Not connected to robot");
        return false;
    }
    
    // 发送回原点指令
    if (!writeRegister(MOTION_CMD, 1)) {  // HOME
        return false;
    }
    
    // 等待指令执行完成
    return waitForMotionComplete();
}

bool RobotDriver::moveJoint(const JointAngles& angles, const MotionParams& params) {
    if (!isConnected()) {
        setError("Not connected to robot");
        return false;
    }
    
    // 设置运动参数
    writeRegister(MOTION_SPEED, params.speed);
    writeRegister(MOTION_ACCEL, params.acceleration);
    
    // 设置目标关节角度 (转换为0.01度精度)
    writeInt32(TARGET_J1, static_cast<int32_t>(angles.joint1 * 100));
    writeInt32(TARGET_J2, static_cast<int32_t>(angles.joint2 * 100));
    writeInt32(TARGET_J3, static_cast<int32_t>(angles.joint3 * 100));
    writeInt32(TARGET_J4, static_cast<int32_t>(angles.joint4 * 100));
    writeInt32(TARGET_J5, static_cast<int32_t>(angles.joint5 * 100));
    writeInt32(TARGET_J6, static_cast<int32_t>(angles.joint6 * 100));

    // 发送关节运动指令
    if (!writeRegister(MOTION_CMD, 2)) {  // MOVEJ
        return false;
    }
    
    return waitForMotionComplete();
}

bool RobotDriver::moveLinear(const Position& position, const MotionParams& params) {
    if (!isConnected()) {
        setError("Not connected to robot");
        return false;
    }
    
    // 设置运动参数
    writeRegister(MOTION_SPEED, params.speed);
    writeRegister(MOTION_ACCEL, params.acceleration);

    // 设置目标位置 (转换为0.1mm和0.01度精度)
    writeInt32(TARGET_X, static_cast<int32_t>(position.x * 10));
    writeInt32(TARGET_Y, static_cast<int32_t>(position.y * 10));
    writeInt32(TARGET_Z, static_cast<int32_t>(position.z * 10));
    writeInt32(TARGET_RX, static_cast<int32_t>(position.rx * 100));
    writeInt32(TARGET_RY, static_cast<int32_t>(position.ry * 100));
    writeInt32(TARGET_RZ, static_cast<int32_t>(position.rz * 100));

    // 发送直线运动指令
    if (!writeRegister(MOTION_CMD, 3)) {  // MOVEL
        return false;
    }
    
    return waitForMotionComplete();
}

bool RobotDriver::stop() {
    if (!isConnected()) {
        setError("Not connected to robot");
        return false;
    }
    
    return writeRegister(MOTION_CMD, 4);  // STOP
}

bool RobotDriver::pause() {
    if (!isConnected()) {
        setError("Not connected to robot");
        return false;
    }
    
    return writeRegister(MOTION_CMD, 5);  // PAUSE
}

bool RobotDriver::resume() {
    if (!isConnected()) {
        setError("Not connected to robot");
        return false;
    }
    
    return writeRegister(MOTION_CMD, 6);  // RESUME
}

bool RobotDriver::setDigitalOutput(int port, bool value) {
    if (!isConnected()) {
        setError("Not connected to robot");
        return false;
    }
    
    if (port < 0 || port > 15) {
        setError("Invalid port number: " + std::to_string(port));
        return false;
    }
    
    // 读取当前数字输出状态
    uint16_t currentOutput = readRegister(DIGITAL_OUT);

    // 设置或清除指定位
    if (value) {
        currentOutput |= (1 << port);
    } else {
        currentOutput &= ~(1 << port);
    }

    return writeRegister(DIGITAL_OUT, currentOutput);
}

bool RobotDriver::getDigitalInput(int port) {
    if (!isConnected()) {
        setError("Not connected to robot");
        return false;
    }
    
    if (port < 0 || port > 15) {
        setError("Invalid port number: " + std::to_string(port));
        return false;
    }
    
    uint16_t digitalInput = readRegister(DIGITAL_IN);
    return (digitalInput & (1 << port)) != 0;
}

bool RobotDriver::controlGripper(int command, int force) {
    if (!isConnected()) {
        setError("Not connected to robot");
        return false;
    }
    
    if (command < 0 || command > 2) {
        setError("Invalid gripper command: " + std::to_string(command));
        return false;
    }
    
    if (force < 1 || force > 100) {
        setError("Invalid gripper force: " + std::to_string(force));
        return false;
    }
    
    // 设置夹爪力度
    writeRegister(GRIPPER_FORCE, force);

    // 发送夹爪指令
    return writeRegister(GRIPPER_CMD, command);
}

int RobotDriver::getGripperStatus() {
    if (!isConnected()) {
        setError("Not connected to robot");
        return -1;
    }
    
    return readRegister(GRIPPER_STATUS);
}

int RobotDriver::executePickPlace(const std::string& sourcePos, const std::string& destPos) {
    if (!isConnected()) {
        setError("Not connected to robot");
        return -1;
    }
    
    uint16_t sourceCode = encodePosition(sourcePos);
    uint16_t destCode = encodePosition(destPos);
    
    if (sourceCode == 0xFFFF || destCode == 0xFFFF) {
        setError("Invalid position code");
        return -1;
    }
    
    // 设置源位置和目标位置
    writeRegister(SOURCE_POSITION, sourceCode);
    writeRegister(DEST_POSITION, destCode);

    // 启动取放任务
    if (!writeRegister(PICK_PLACE_CMD, 1)) {
        return -1;
    }
    
    // 返回任务ID (从寄存器读取)
    uint16_t taskIdHigh = readRegister(TASK_ID_HIGH);
    uint16_t taskIdLow = readRegister(TASK_ID_LOW);
    
    return (static_cast<int>(taskIdHigh) << 16) | taskIdLow;
}

int RobotDriver::getTaskStatus(int taskId) {
    if (!isConnected()) {
        setError("Not connected to robot");
        return -1;
    }
    
    return readRegister(PICK_PLACE_STATUS);
}

double RobotDriver::getTaskProgress() {
    if (!isConnected()) {
        setError("Not connected to robot");
        return 0.0;
    }
    
    uint32_t progress = static_cast<uint32_t>(readInt32(TASK_PROGRESS));
    return progress / 100.0;  // 转换为百分比
}

void RobotDriver::setStatusCallback(StatusCallback callback) {
    std::lock_guard<std::mutex> lock(m_mutex);
    m_statusCallback = callback;
}

std::string RobotDriver::getLastError() const {
    std::lock_guard<std::mutex> lock(m_mutex);
    return m_lastError;
}

// ========== 私有方法实现 ==========

uint16_t RobotDriver::readRegister(int address) {
    if (!m_modbusServer) {
        setError("MODBUS server is null");
        return 0;
    }

    return m_modbusServer->readRegister(address);
}

bool RobotDriver::writeRegister(int address, uint16_t value) {
    if (!m_modbusServer) {
        setError("MODBUS server is null");
        return false;
    }

    m_modbusServer->writeRegister(address, value);
    return true;
}

int32_t RobotDriver::readInt32(int address) {
    if (!m_modbusServer) {
        setError("MODBUS server is null");
        return 0;
    }

    return m_modbusServer->readInt32(address);
}

bool RobotDriver::writeInt32(int address, int32_t value) {
    if (!m_modbusServer) {
        setError("MODBUS server is null");
        return false;
    }

    m_modbusServer->writeInt32(address, value);
    return true;
}

uint16_t RobotDriver::encodePosition(const std::string& position) {
    auto it = s_positionCodes.find(position);
    return (it != s_positionCodes.end()) ? it->second : 0xFFFF;
}



void RobotDriver::setStatus(RobotStatus status, const std::string& message) {
    m_status.store(status);
    
    if (m_statusCallback) {
        m_statusCallback(status, message);
    }
}

void RobotDriver::setError(const std::string& error) {
    std::lock_guard<std::mutex> lock(m_mutex);
    m_lastError = error;
}

void RobotDriver::handleRegisterUpdate(int address, uint16_t value) {
    // 处理来自机器人的寄存器更新
    // 这里可以根据地址和值来更新内部状态

    // 例如：处理状态更新
    if (address == ModbusRegisters::SYSTEM_STATUS) {
        // 更新系统状态
    } else if (address == ModbusRegisters::MOTION_STATUS) {
        // 更新运动状态
    }
    // 可以根据需要添加更多处理逻辑
}

bool RobotDriver::waitForMotionComplete() {
    const int maxWaitTime = 30000;  // 最大等待30秒
    const int checkInterval = 100;  // 每100ms检查一次

    for (int elapsed = 0; elapsed < maxWaitTime; elapsed += checkInterval) {
        uint16_t cmdStatus = readRegister(MOTION_STATUS);

        if (cmdStatus == 2) {  // 完成
            return true;
        } else if (cmdStatus == 3) {  // 错误
            setError("Motion command failed");
            return false;
        }

        std::this_thread::sleep_for(std::chrono::milliseconds(checkInterval));
    }

    setError("Motion command timeout");
    return false;
}

} // namespace Robot
} // namespace AnalysisRobot
