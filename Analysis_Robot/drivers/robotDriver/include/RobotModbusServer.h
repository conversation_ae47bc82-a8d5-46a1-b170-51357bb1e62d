#ifndef ROBOT_MODBUS_SERVER_H
#define ROBOT_MODBUS_SERVER_H

#include <modbus.h>
#include <memory>
#include <thread>
#include <atomic>
#include <mutex>
#include <functional>
#include <map>
#include <string>

namespace AnalysisRobot {
namespace Robot {

// 前向声明
struct Position;
struct JointAngles;
struct MotionParams;
enum class RobotStatus;

/**
 * @brief MODBUS寄存器地址定义
 */
namespace ModbusRegisters {
    // 系统状态寄存器 (0x0000-0x000F)
    constexpr int SYSTEM_STATUS = 0x0000;      // 系统状态
    constexpr int ERROR_CODE = 0x0001;         // 错误代码
    constexpr int ROBOT_MODE = 0x0002;         // 机器人模式
    constexpr int SAFETY_STATUS = 0x0003;      // 安全状态
    constexpr int CONNECTION_STATUS = 0x0004;  // 连接状态
    constexpr int HEARTBEAT = 0x0005;          // 心跳计数器
    constexpr int TASK_ID_HIGH = 0x0006;       // 任务ID高位
    constexpr int TASK_ID_LOW = 0x0007;        // 任务ID低位
    
    // 位置反馈寄存器 (0x0010-0x002F)
    constexpr int CURRENT_X = 0x0010;          // 当前X位置 (32位)
    constexpr int CURRENT_Y = 0x0012;          // 当前Y位置 (32位)
    constexpr int CURRENT_Z = 0x0014;          // 当前Z位置 (32位)
    constexpr int CURRENT_RX = 0x0016;         // 当前RX角度 (32位)
    constexpr int CURRENT_RY = 0x0018;         // 当前RY角度 (32位)
    constexpr int CURRENT_RZ = 0x001A;         // 当前RZ角度 (32位)
    
    constexpr int CURRENT_J1 = 0x001C;         // 当前关节1角度 (32位)
    constexpr int CURRENT_J2 = 0x001E;         // 当前关节2角度 (32位)
    constexpr int CURRENT_J3 = 0x0020;         // 当前关节3角度 (32位)
    constexpr int CURRENT_J4 = 0x0022;         // 当前关节4角度 (32位)
    constexpr int CURRENT_J5 = 0x0024;         // 当前关节5角度 (32位)
    constexpr int CURRENT_J6 = 0x0026;         // 当前关节6角度 (32位)

    // 目标关节角度寄存器 (0x0040-0x004B)
    constexpr int TARGET_J1 = 0x0040;          // 目标关节1角度 (32位)
    constexpr int TARGET_J2 = 0x0042;          // 目标关节2角度 (32位)
    constexpr int TARGET_J3 = 0x0044;          // 目标关节3角度 (32位)
    constexpr int TARGET_J4 = 0x0046;          // 目标关节4角度 (32位)
    constexpr int TARGET_J5 = 0x0048;          // 目标关节5角度 (32位)
    constexpr int TARGET_J6 = 0x004A;          // 目标关节6角度 (32位)
    
    // 运动控制寄存器 (0x0030-0x004F)
    constexpr int MOTION_CMD = 0x0030;         // 运动指令
    constexpr int MOTION_STATUS = 0x0031;      // 运动状态
    constexpr int TARGET_X = 0x0032;           // 目标X位置 (32位)
    constexpr int TARGET_Y = 0x0034;           // 目标Y位置 (32位)
    constexpr int TARGET_Z = 0x0036;           // 目标Z位置 (32位)
    constexpr int TARGET_RX = 0x0038;          // 目标RX角度 (32位)
    constexpr int TARGET_RY = 0x003A;          // 目标RY角度 (32位)
    constexpr int TARGET_RZ = 0x003C;          // 目标RZ角度 (32位)
    constexpr int MOTION_SPEED = 0x003E;       // 运动速度
    constexpr int MOTION_ACCEL = 0x003F;       // 运动加速度
    
    // IO控制寄存器 (0x0050-0x005F)
    constexpr int DIGITAL_OUT = 0x0050;        // 数字输出
    constexpr int DIGITAL_IN = 0x0051;         // 数字输入
    constexpr int GRIPPER_CMD = 0x0052;        // 夹爪指令
    constexpr int GRIPPER_STATUS = 0x0053;     // 夹爪状态
    constexpr int GRIPPER_FORCE = 0x0054;      // 夹爪力度
    
    // 任务管理寄存器 (0x0070-0x007F)
    constexpr int TASK_CMD = 0x0070;           // 任务指令
    constexpr int TASK_STATUS = 0x0071;        // 任务状态
    constexpr int TASK_PROGRESS = 0x0072;      // 任务进度 (32位)
    constexpr int PICK_PLACE_CMD = 0x0074;     // 取放指令
    constexpr int PICK_PLACE_STATUS = 0x0075;  // 取放状态
    constexpr int SOURCE_POSITION = 0x0076;    // 源位置编码
    constexpr int DEST_POSITION = 0x0077;      // 目标位置编码
}

/**
 * @brief 机器人MODBUS服务端类
 * 
 * 实现MODBUS TCP服务端功能，接受机器人客户端的连接和请求
 */
class RobotModbusServer {
public:
    /**
     * @brief 寄存器更新回调函数类型
     * @param address 寄存器地址
     * @param value 新值
     */
    using RegisterUpdateCallback = std::function<void(int address, uint16_t value)>;
    
    /**
     * @brief 连接状态回调函数类型
     * @param connected 是否连接
     */
    using ConnectionCallback = std::function<void(bool connected)>;

public:
    /**
     * @brief 构造函数
     */
    RobotModbusServer();
    
    /**
     * @brief 析构函数
     */
    ~RobotModbusServer();
    
    /**
     * @brief 初始化服务器
     * @param ip 监听IP地址
     * @param port 监听端口
     * @return 是否成功
     */
    bool initialize(const std::string& ip, int port);
    
    /**
     * @brief 启动服务器
     * @return 是否成功
     */
    bool start();
    
    /**
     * @brief 停止服务器
     */
    void stop();
    
    /**
     * @brief 检查是否有客户端连接
     * @return 是否有连接
     */
    bool isClientConnected() const;
    
    /**
     * @brief 读取寄存器值
     * @param address 寄存器地址
     * @return 寄存器值
     */
    uint16_t readRegister(int address);
    
    /**
     * @brief 写入寄存器值
     * @param address 寄存器地址
     * @param value 写入值
     */
    void writeRegister(int address, uint16_t value);
    
    /**
     * @brief 读取32位值
     * @param address 起始地址
     * @return 32位值
     */
    int32_t readInt32(int address);
    
    /**
     * @brief 写入32位值
     * @param address 起始地址
     * @param value 32位值
     */
    void writeInt32(int address, int32_t value);
    
    /**
     * @brief 设置寄存器更新回调
     * @param callback 回调函数
     */
    void setRegisterUpdateCallback(RegisterUpdateCallback callback);
    
    /**
     * @brief 设置连接状态回调
     * @param callback 回调函数
     */
    void setConnectionCallback(ConnectionCallback callback);
    
    /**
     * @brief 获取最后错误信息
     * @return 错误信息
     */
    std::string getLastError() const;

private:
    modbus_t* m_ctx;                           // MODBUS上下文
    modbus_mapping_t* m_mapping;               // 寄存器映射
    std::string m_ip;                          // 监听IP
    int m_port;                                // 监听端口
    int m_serverSocket;                        // 服务器套接字
    int m_clientSocket;                        // 客户端套接字
    
    std::atomic<bool> m_running;               // 运行状态
    std::atomic<bool> m_clientConnected;       // 客户端连接状态
    std::thread m_serverThread;                // 服务器线程
    std::thread m_heartbeatThread;             // 心跳线程
    
    mutable std::mutex m_mutex;                // 线程安全锁
    std::string m_lastError;                   // 最后错误信息
    
    RegisterUpdateCallback m_registerCallback; // 寄存器更新回调
    ConnectionCallback m_connectionCallback;   // 连接状态回调
    
    uint16_t m_heartbeatCounter;               // 心跳计数器
    
    /**
     * @brief 服务器主循环
     */
    void serverLoop();
    
    /**
     * @brief 心跳循环
     */
    void heartbeatLoop();
    
    /**
     * @brief 处理客户端连接
     */
    void handleClientConnection();
    
    /**
     * @brief 设置错误信息
     * @param error 错误信息
     */
    void setError(const std::string& error);
    
    /**
     * @brief 清理资源
     */
    void cleanup();
};

} // namespace Robot
} // namespace AnalysisRobot

#endif // ROBOT_MODBUS_SERVER_H
